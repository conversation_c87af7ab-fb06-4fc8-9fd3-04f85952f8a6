from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import date


class SalesRequest(models.Model):
    _inherit = "kb_request_for_sale"

    """
    This class customizes purchase order creation for sales requests:

    1. kb_create_purchase_order_shipment():
       - Creates purchase orders ONLY for lines with kb_price_shipment > 0
       - Uses actual product names instead of "Shipment Cost"

    2. kb_create_purchase_order():
       - Creates regular purchase orders EXCLUDING lines with kb_price_shipment > 0
       - Lines with shipment prices should be handled by kb_create_purchase_order_shipment()

    This separation ensures that:
    - Regular products without shipment costs go to regular purchase orders
    - Products with shipment costs go to shipment-specific purchase orders
    - No duplication of lines between the two types of purchase orders
    """

    def _get_lines_with_shipment_price(self):
        """Helper method to get lines that have shipment prices"""
        return self.kb_product_line_ids.filtered(lambda x: x.kb_price_shipment and x.kb_price_shipment > 0)

    def _get_lines_without_shipment_price(self):
        """Helper method to get lines that don't have shipment prices"""
        return self.kb_product_line_ids.filtered(lambda x: not x.kb_price_shipment or x.kb_price_shipment <= 0)

    def kb_create_purchase_order_shipment(self):
        """
        Override the shipment purchase order creation to use actual product names
        instead of creating/using "Shipment Cost" service product.
        """
        if not self.kb_shipment_vendor_id:
            raise ValidationError("Please Enter a Vendor")
        
        for rec in self:
            # Check if there are any lines with shipment prices before proceeding
            lines_with_shipment = rec._get_lines_with_shipment_price()

            if not lines_with_shipment:
                raise ValidationError("No lines with shipment price found. Please set shipment prices for the products you want to include in the purchase order.")

            if self.kb_shipment_vendor_id.id == 8:
                self._crate_saleorder_othercompany()
                self._crate_transfer_othercompany()

            today_date = date.today()
            branch_id = self.env.user.branch_id
            analytic = self.env['account.analytic.account'].search(
                [('kb_branch_id', '=', branch_id.id)],
                limit=1)
            analytic_account_id = rec.kb_analytic_account_id if rec.kb_analytic_account_id else analytic

            purchase_order_id = self.env['purchase.order'].create({
                'partner_id': self.kb_shipment_vendor_id.id,
                'kb_ref_1_po': rec.kb_ref_1_am,
                'kb_ref_2_po': rec.kb_ref_2_am,
                'req_id': self.id,
                'company_id': self.company_id.id,
                'branch_id': branch_id.id,
                'date_order': rec.kb_date,
                'user_id': rec.kb_sales_rep.id
            })

            delivery_list = []

            for x in lines_with_shipment:
                # Calculate shipment price based on tax settings
                if x.kb_shipment_tax == 'add_vat':
                    kb_price_shipment = x.kb_price_shipment
                    r = [(6, 0, (45,))]
                elif x.kb_shipment_tax == 'include_vat':
                    kb_price_shipment = x.kb_price_shipment / 1.15
                    r = [(6, 0, (5,))]
                else:
                    kb_price_shipment = x.kb_price_shipment
                    r = None

                # Use the actual product instead of "Shipment Cost"
                if not x.kb_product_id_pro:
                    raise ValidationError(f"Product is required for line with shipment price {x.kb_price_shipment}")

                # Check if product is service and if shipment vendor has supplier info for this product
                if (x.kb_product_id_pro.detailed_type == 'service' and
                    self.kb_shipment_vendor_id):

                    # Search for supplier info where partner matches shipment vendor
                    supplier_info = self.env['product.supplierinfo'].search([
                        ('partner_id', '=', self.kb_shipment_vendor_id.id),
                        '|',
                        ('product_id', '=', x.kb_product_id_pro.id),
                        ('product_tmpl_id', '=', x.kb_product_id_pro.product_tmpl_id.id)
                    ], limit=1)

                    # If supplier info found, use its price for kb_price_shipment
                    if supplier_info:
                        kb_price_shipment = supplier_info.price
                        # Recalculate based on tax settings with the new price
                        if x.kb_shipment_tax == 'add_vat':
                            kb_price_shipment = supplier_info.price
                        elif x.kb_shipment_tax == 'include_vat':
                            kb_price_shipment = supplier_info.price / 1.15

                delivery_list.append(self.env['purchase.order.line'].sudo().create({
                    'product_id': x.kb_product_id_pro.id,
                    'name': x.kb_product_id_pro.name,  # Use actual product name
                    'product_qty': 1,
                    'qty_received': 1,
                    'price_unit': kb_price_shipment,
                    'taxes_id': r,
                    'order_id': purchase_order_id.id,
                    'company_id': self.company_id.id,
                    'analytic_distribution': {analytic_account_id.id: 100.0}
                }))
            
            rec.kb_bool_purchase_shipment = True
            bill1 = purchase_order_id.button_confirm()
            bill2 = purchase_order_id.action_create_invoice()
            
            # Handle landed costs if needed
            for inv in purchase_order_id.invoice_ids.invoice_line_ids:
                if not inv.is_landed_costs_line:
                    inv.is_landed_costs_line = True

    def kb_create_purchase_order(self):
        """
        Override the regular purchase order creation to exclude lines that have shipment prices.
        Lines with kb_price_shipment > 0 should be handled by kb_create_purchase_order_shipment instead.
        """
        # Temporarily filter out lines with shipment prices before calling super()
        original_lines = self.kb_product_line_ids
        lines_without_shipment = self._get_lines_without_shipment_price()

        print(f"@@@@@ Regular PO Creation: Total lines: {len(original_lines)}, Lines without shipment: {len(lines_without_shipment)}")

        # Temporarily replace the lines to exclude shipment lines
        self.kb_product_line_ids = lines_without_shipment

        try:
            # Call the parent method with filtered lines
            res = super().kb_create_purchase_order()

            # Process the created purchase orders
            for order in self:
                domain = [('req_id', '=', self.id)]
                purchase_order_ids = order.env['purchase.order'].sudo().search(domain, order="id asc")
                if purchase_order_ids:
                    purchase_order_id = purchase_order_ids[-1]
                    print("@@@@@@@@@@@purchase_order_id", purchase_order_id)
                    purchase_order_id.sudo().update({
                        'date_order': order.kb_date,
                        'date_approve': order.kb_date,
                        'date_planned': order.kb_date,
                        'user_id': order.kb_sales_rep.id,
                    })
                    branch_id = self.env.user.branch_id
                    analytic = self.env['account.analytic.account'].search(
                        [('kb_branch_id', '=', branch_id.id)],
                        limit=1)
                    analytic_account_id = order.kb_analytic_account_id if order.kb_analytic_account_id else analytic
                    purchase_order_id.order_line.update(
                        {
                            'analytic_distribution': {analytic_account_id.id: 100}
                        }
                    )
            return res
        finally:
            # Always restore the original lines
            self.kb_product_line_ids = original_lines

    def kb_create_sale_order(self):
        """
        Override the sale order creation to only include lines that have shipment prices.
        Only lines with kb_price_shipment > 0 will be included in the sale order.
        """
        # Temporarily filter to only lines with shipment prices before calling super()
        original_lines = self.kb_product_line_ids
        lines_with_shipment = self._get_lines_with_shipment_price()

        print(f"@@@@@ Sale Order Creation: Total lines: {len(original_lines)}, Lines with shipment: {len(lines_with_shipment)}")

        if not lines_with_shipment:
            raise ValidationError("No lines with shipment price found. Please set shipment prices for the products you want to include in the sale order.")

        # Temporarily replace the lines to include only shipment lines
        self.kb_product_line_ids = lines_with_shipment

        try:
            # Call the parent method with filtered lines
            res = super().kb_create_sale_order()

            # Process the created sale orders
            for order in self:
                total_due_amount = order.kb_partner_id.payment_amount_due + order.kb_total_sales_price
                credit_amount = order.kb_partner_id.kb_credit_limit
                print("total_due_amount", total_due_amount, "credit_amount", credit_amount)
                if total_due_amount > credit_amount and order.kb_partner_id.is_kb_credit:
                    raise ValidationError(f"Not Allowed To Pass Credit Limit {credit_amount}\n"
                                          f"Total Due Amount {total_due_amount}")
                domain = [('req_id', '=', self.id)]
                sale_order_id = order.env['sale.order'].sudo().search(domain)
                if sale_order_id:
                    branch_id = self.env.user.branch_id
                    analytic = self.env['account.analytic.account'].search(
                        [('kb_branch_id', '=', branch_id.id)],
                        limit=1)
                    analytic_account_id = order.kb_analytic_account_id if order.kb_analytic_account_id else analytic

                    sale_order_id.update({
                        'branch_id': branch_id.id,
                        'analytic_account_id': analytic.id,
                        'date_order': order.kb_date,
                        'user_id': order.kb_sales_rep.id
                    })
                    sale_order_id.order_line.update(
                        {
                            'analytic_distribution': {analytic_account_id.id: 100}
                        }
                    )
            return res
        finally:
            # Always restore the original lines
            self.kb_product_line_ids = original_lines


class SalesRequestLine(models.Model):
    _inherit = "kb_request_for_sale_line"

    @api.onchange('kb_product_id_pro', 'kb_product_line_ids.kb_shipment_vendor_id', 'kb_product_line_ids.kb_vendor_id')
    def _onchange_product_supplier_prices(self):
        """
        Automatically update prices based on supplier information:
        1. For service products: update kb_price_shipment when kb_shipment_vendor_id matches supplier
        2. For regular products: update kb_product_cost when kb_vendor_id matches supplier
        """
        for line in self:
            if line.kb_product_id_pro and line.kb_product_line_ids:

                # Case 1: Service products - update shipment price
                if (line.kb_product_id_pro.detailed_type == 'service' and
                    line.kb_product_line_ids.kb_shipment_vendor_id):

                    # Search for supplier info where partner matches shipment vendor
                    supplier_info = line.env['product.supplierinfo'].search([
                        ('partner_id', '=', line.kb_product_line_ids.kb_shipment_vendor_id.id),
                        '|',
                        ('product_id', '=', line.kb_product_id_pro.id),
                        ('product_tmpl_id', '=', line.kb_product_id_pro.product_tmpl_id.id)
                    ], limit=1)

                    # If supplier info found, update kb_price_shipment with supplier price
                    if supplier_info:
                        line.kb_price_shipment = supplier_info.price

                # Case 2: Regular products (consu/product) - update product cost
                elif (line.kb_product_id_pro.detailed_type in ['consu', 'product'] and
                      line.kb_product_line_ids.kb_vendor_id):

                    # Search for supplier info where partner matches vendor
                    supplier_info = line.env['product.supplierinfo'].search([
                        ('partner_id', '=', line.kb_product_line_ids.kb_vendor_id.id),
                        '|',
                        ('product_id', '=', line.kb_product_id_pro.id),
                        ('product_tmpl_id', '=', line.kb_product_id_pro.product_tmpl_id.id)
                    ], limit=1)

                    # If supplier info found, update kb_product_cost with supplier price
                    if supplier_info:
                        line.kb_product_cost = supplier_info.price
