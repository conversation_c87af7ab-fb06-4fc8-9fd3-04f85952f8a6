# KB Purchase Shipment Update Module

## Overview

This module customizes the purchase order creation process for sales requests to handle shipment costs separately from regular product costs.

## Key Features

### 1. Separated Order Creation

The module provides distinct methods for creating both purchase orders and sale orders with shipment filtering:

#### `kb_create_purchase_order_shipment()`
- **Purpose**: Creates purchase orders specifically for shipment costs
- **Criteria**: Only processes lines where `kb_price_shipment > 0`
- **Behavior**: 
  - Uses actual product names instead of generic "Shipment Cost"
  - Applies tax calculations based on `kb_shipment_tax` field
  - Automatically uses supplier prices for service products when available
  - Validates that at least one line has shipment price before creating PO

#### `kb_create_purchase_order()`
- **Purpose**: Creates regular purchase orders for product costs
- **Criteria**: Only processes lines where `kb_price_shipment <= 0` or is empty
- **Behavior**:
  - Excludes lines that have shipment prices (those are handled by shipment method)
  - Applies standard purchase order logic
  - Updates dates and analytic distribution

### 2. No Duplication

The separation ensures that:
- Lines with shipment prices are ONLY processed by `kb_create_purchase_order_shipment()`
- Lines without shipment prices are ONLY processed by `kb_create_purchase_order()`
- No line appears in both types of purchase orders

### 3. Automatic Price Population

The module includes onchange methods that automatically populate prices from supplier information:
- **Service products**: Updates `kb_price_shipment` when `kb_shipment_vendor_id` matches supplier
- **Regular products**: Updates `kb_product_cost` when `kb_vendor_id` matches supplier

## Usage Workflow

1. **Set up products** with appropriate supplier information in `product.supplierinfo`
2. **Create sales request** with product lines
3. **Set shipment vendor** for lines that need shipment costs
4. **Prices auto-populate** based on supplier information
5. **Create purchase orders**:
   - Call `kb_create_purchase_order()` for regular products
   - Call `kb_create_purchase_order_shipment()` for shipment costs

## Example Scenario

Sales request with 3 lines:
- Line 1: Product A, `kb_price_shipment = 0` → Goes to regular PO
- Line 2: Product B, `kb_price_shipment = 50` → Goes to shipment PO  
- Line 3: Product C, `kb_price_shipment = 0` → Goes to regular PO

Result:
- Regular PO contains: Product A, Product C
- Shipment PO contains: Product B

## Error Handling

- **Shipment PO**: Validates that at least one line has shipment price
- **Regular PO**: Gracefully handles cases where all lines have shipment prices
- **Product validation**: Ensures products are specified for lines with shipment prices

## Testing

The module includes comprehensive tests covering:
- Creating shipment POs with valid shipment prices
- Validation when no shipment prices are set
- Mixed scenarios with some lines having shipment prices
- Regular PO creation excluding shipment lines
