{
    'name': 'KB Purchase Shipment Update',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Customize purchase order shipment to use actual product names and filter by shipment price',
    'description': """
        This module customizes the kb_create_purchase_order_shipment method to:
        - Use actual product names from kb_product_id_pro.name instead of "Shipment Cost"
        - Only create purchase order lines for products that have kb_price_shipment values
        - Remove dependency on creating/searching for "Shipment Cost" service product
        - Validate that at least one line has shipment price before creating purchase order
        - Automatically populate shipment prices from supplier info for service products
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'kb_sales_request_update',
        'kb_request_for_Sale',
    ],
    'data': [],
    'installable': True,
    'auto_install': False,
    'application': False,
}
