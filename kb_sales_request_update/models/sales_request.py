from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import datetime, date, timedelta


class SalesRequest(models.Model):
    _inherit = "kb_request_for_sale"

    customer_price_list_id = fields.Many2one(
        comodel_name='product.pricelist',
        string='Customer PriceList',
        required=False)
    vendor_price_list_id = fields.Many2one(
        comodel_name='product.pricelist',
        string='Vendor PriceList',
        required=False)
    kb_sales_rep = fields.Many2one(
        comodel_name='res.users',
        string='Sales rep',
        required=True, domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
    kb_analytic_account_id = fields.Many2one(
        comodel_name='account.analytic.account',
        string='Analytic Account',
        required=True)

    def _get_report_base_filename(self):
        for order in self:
            total_name = f"Sales Request {order.kb_sales_ids} {order.kb_partner_id.name}"
            return total_name

    def kb_create_purchase_order(self):
        res = super().kb_create_purchase_order()
        for order in self:
            domain = [('req_id', '=', self.id)]
            purchase_order_ids = order.env['purchase.order'].sudo().search(domain, order="id asc")
            if purchase_order_ids:
                purchase_order_id = purchase_order_ids[-1]
                print("@@@@@@@@@@@purchase_order_id", purchase_order_id)
                purchase_order_id.sudo().update({
                    'date_order': order.kb_date,
                    'date_approve': order.kb_date,
                    'date_planned': order.kb_date,
                    'user_id': order.kb_sales_rep.id,
                })
                branch_id = self.env.user.branch_id
                analytic = self.env['account.analytic.account'].search(
                    [('kb_branch_id', '=', branch_id.id)],
                    limit=1)
                analytic_account_id = order.kb_analytic_account_id if order.kb_analytic_account_id else analytic
                purchase_order_id.order_line.update(
                    {
                        'analytic_distribution': {analytic_account_id.id: 100}
                    }
                )
        return res

    def kb_create_sale_order(self):
        res = super().kb_create_sale_order()
        for order in self:
            total_due_amount = order.kb_partner_id.payment_amount_due + order.kb_total_sales_price
            credit_amount = order.kb_partner_id.kb_credit_limit
            print("total_due_amount", total_due_amount, "credit_amount", credit_amount)
            if total_due_amount > credit_amount and order.kb_partner_id.is_kb_credit:
                raise ValidationError(f"Not Allowed To Pass Credit Limit {credit_amount}\n"
                                      f"Total Due Amount {total_due_amount}")
            domain = [('req_id', '=', self.id)]
            sale_order_id = order.env['sale.order'].sudo().search(domain)
            if sale_order_id:
                branch_id = self.env.user.branch_id
                analytic = self.env['account.analytic.account'].search(
                    [('kb_branch_id', '=', branch_id.id)],
                    limit=1)
                analytic_account_id = order.kb_analytic_account_id if order.kb_analytic_account_id else analytic

                sale_order_id.update({
                    'branch_id': branch_id.id,
                    'analytic_account_id': analytic.id,
                    'date_order': order.kb_date,
                    'user_id': order.kb_sales_rep.id
                })
                sale_order_id.order_line.update(
                    {
                        'analytic_distribution': {analytic_account_id.id: 100}
                    }
                )
        return res

    def _prepare_invoice_line(self, **optional_values):
        res = super(SalesRequest, self)._prepare_invoice_line(**optional_values)
        branch_id = self.env.user.branch_id
        analytic = self.env['account.analytic.account'].search(
            [('kb_branch_id', '=', branch_id.id)],
            limit=1)
        res.update(
            {
                'analytic_distribution': {analytic.id: 100}
            }
        )
        return res

    def kb_create_purchase_order_othercost(self):

        if not self.kb_other_vendor_id:
            raise ValidationError("Please Enter a Vendor")
        for rec in self:
            today_date = date.today()
            branch_id = self.env.user.branch_id
            analytic = self.env['account.analytic.account'].search(
                [('kb_branch_id', '=', branch_id.id)],
                limit=1)
            analytic_account_id = self.kb_analytic_account_id if self.kb_analytic_account_id else analytic
            purchase_order_id = self.env['purchase.order'].create({
                'partner_id': self.kb_other_vendor_id.id,
                'kb_ref_1_po': rec.kb_ref_1_am,
                'kb_ref_2_po': rec.kb_ref_2_am,
                'req_id': self.id,
                'company_id': self.company_id.id,
                'branch_id': branch_id.id,
                'date_order': self.kb_date,
                'user_id': self.kb_sales_rep.id
            })
            delivery_list = []
            for x in rec.kb_product_line_ids:
                kb_othercost_shipment = 0
                if x.kb_othercost_tax == 'add_vat':
                    kb_othercost_shipment = x.kb_othercost_shipment
                    r = [(6, 0, (45,))]
                elif x.kb_othercost_tax == 'include_vat':
                    kb_othercost_shipment = x.kb_othercost_shipment
                    r = [(6, 0, (5,))]
                else:
                    kb_othercost_shipment = x.kb_othercost_shipment
                    r = None
                product_othercost = self.env['product.template'].search([('name', '=', 'Other Cost')])
                if product_othercost:
                    pass
                else:
                    product_othercost = self.env['product.template'].create({
                        'name': "Other Cost"
                    })

                delivery_list.append(self.env['purchase.order.line'].create({
                    'product_id': product_othercost.product_variant_id.id,
                    'name': product_othercost.product_variant_id.name,
                    'product_qty': 1,
                    'price_unit': kb_othercost_shipment,
                    'taxes_id': r,
                    'order_id': purchase_order_id.id,
                    'company_id': self.company_id.id,
                    'analytic_distribution': {analytic_account_id.id: 100.0}
                })
                )
            rec.kb_bool_purchase_othercost = True
            bill1 = purchase_order_id.button_confirm()
            bill2 = purchase_order_id.action_create_invoice()
            for inv in purchase_order_id.invoice_ids.invoice_line_ids:
                if not inv.is_landed_costs_line:
                    inv.is_landed_costs_line = True
            purchase_order_id.invoice_ids.button_create_landed_costs()
            if self.kb_stock_picking_id:
                for linex in purchase_order_id.invoice_ids.landed_costs_ids:
                    for ll in self.kb_stock_picking_id:
                        linex.picking_ids = [(6, 0, (ll.id,))]

    def kb_create_purchase_order_shipment(self):
        if not self.kb_shipment_vendor_id:
            raise ValidationError("Please Enter a Vendor")
        for rec in self:
            if self.kb_shipment_vendor_id.id == 8:
                self._crate_saleorder_othercompany()
                self._crate_transfer_othercompany()
            today_date = date.today()
            branch_id = self.env.user.branch_id
            analytic = self.env['account.analytic.account'].search(
                [('kb_branch_id', '=', branch_id.id)],
                limit=1)
            analytic_account_id = rec.kb_analytic_account_id if rec.kb_analytic_account_id else analytic
            purchase_order_id = self.env['purchase.order'].create({
                'partner_id': self.kb_shipment_vendor_id.id,
                'kb_ref_1_po': rec.kb_ref_1_am,
                'kb_ref_2_po': rec.kb_ref_2_am,
                'req_id': self.id,
                'company_id': self.company_id.id,
                'branch_id': branch_id.id,
                'date_order': rec.kb_date,
                'user_id': rec.kb_sales_rep.id
            })
            delivery_list = []
            for x in rec.kb_product_line_ids:
                # r = None
                if x.kb_shipment_tax == 'add_vat':
                    kb_price_shipment = x.kb_price_shipment
                    r = [(6, 0, (45,))]
                elif x.kb_shipment_tax == 'include_vat':
                    kb_price_shipment = x.kb_price_shipment / 1.15
                    r = [(6, 0, (5,))]
                else:
                    kb_price_shipment = x.kb_price_shipment
                    r = None
                product_shipment = self.env['product.template'].sudo().search(
                    [('name', '=', 'Shipment Cost'), ('detailed_type', '=', 'service')], limit=1)
                if not product_shipment:
                    #     pass
                    # else:
                    product_shipment = self.env['product.template'].create({
                        'name': "Shipment Cost",
                        "detailed_type": "service",
                        "landed_cost_ok": True,
                        "split_method_landed_cost": "equal",
                    })

                delivery_list.append(self.env['purchase.order.line'].sudo().create({
                    'product_id': product_shipment.product_variant_id.id,
                    'name': product_shipment.product_variant_id.name,
                    'product_qty': 1,
                    'qty_received': 1,
                    'price_unit': kb_price_shipment,
                    'taxes_id': r,
                    'order_id': purchase_order_id.id,
                    'company_id': self.company_id.id,
                    'analytic_distribution': {analytic_account_id.id: 100.0}
                })
                )
            rec.kb_bool_purchase_shipment = True
            bill1 = purchase_order_id.button_confirm()
            bill2 = purchase_order_id.action_create_invoice()
            for inv in purchase_order_id.invoice_ids.invoice_line_ids:
                if not inv.is_landed_costs_line:
                    inv.is_landed_costs_line = True

            purchase_order_id.invoice_ids.button_create_landed_costs()
            if self.kb_stock_picking_id:
                for linex in purchase_order_id.invoice_ids.landed_costs_ids:
                    for ll in self.kb_stock_picking_id:
                        linex.picking_ids = [(6, 0, (ll.id,))]



    @api.onchange("kb_partner_id", "kb_vendor_id")
    def get_price_list(self):
        for order in self:
            if order.kb_partner_id:
                order.customer_price_list_id = order.kb_partner_id.property_product_pricelist
            if order.kb_vendor_id:
                order.vendor_price_list_id = order.kb_vendor_id.property_product_pricelist


class SalesRequestLines(models.Model):
    _inherit = "kb_request_for_sale_line"

    @api.onchange("kb_product_id")
    def get_product_price_list(self):
        for line in self:
            if line.kb_product_id:
                customer_price_list_id = line.kb_product_line_ids.customer_price_list_id
                # vendor_price_list_id = line.kb_product_line_ids.vendor_price_list_id
                customer_price_line = customer_price_list_id.item_ids.filtered(
                    lambda x: x.product_tmpl_id == line.kb_product_id)
                # vendor_price_line = vendor_price_list_id.item_ids.filtered(
                #     lambda x: x.product_tmpl_id == line.kb_product_id)
                line.kb_product_price = customer_price_line.fixed_price if customer_price_line else 0
                # line.kb_product_cost = vendor_price_line.fixed_price if vendor_price_line else 0
